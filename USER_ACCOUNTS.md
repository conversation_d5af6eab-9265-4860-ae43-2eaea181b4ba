# 👥 User Accounts - TiXara Event Management System

Dokumen ini berisi informasi lengkap tentang akun user yang telah dibuat untuk setiap role dalam sistem tixara.

## 🚀 Cara Menjalankan Seeder

Untuk membuat semua akun user, jalankan perintah berikut:

```bash
# Jalankan seeder user
php artisan db:seed --class=UserSeeder

# Atau jalankan semua seeder sekaligus
php artisan migrate:fresh --seed
```

## 🔑 Login Credentials

### 👑 ADMIN ACCOUNTS
**Role:** Administrator - Aks<PERSON> penuh ke seluruh sistem

| Nama | Email | Password | Deskripsi |
|------|-------|----------|-----------|
| Super Admin | <EMAIL> | TikPro@2024 | Admin utama sistem |
| Admin Sarah | <EMAIL> | TikPro@2024 | Admin sekunder |

**Hak Akses Admin:**
- ✅ Mengelola semua user
- ✅ Mengelola kategori event
- ✅ Moderasi semua event
- ✅ Melihat laporan dan statistik
- ✅ Konfigurasi sistem
- ✅ Mengelola pembayaran dan refund

---

### 👥 STAFF ACCOUNTS
**Role:** Staff - Membantu operasional dan validasi tiket

| Nama | Email | Password | Deskripsi |
|------|-------|----------|-----------|
| Staff Budi | <EMAIL> | staff123 | Staff operasional |
| Staff Sari | <EMAIL> | staff123 | Staff customer service |
| Staff Andi | <EMAIL> | staff123 | Staff validasi tiket |

**Hak Akses Staff:**
- ✅ Validasi tiket di lokasi event
- ✅ Membantu customer service
- ✅ Melihat data event terbatas
- ✅ Memproses refund (dengan approval)
- ❌ Tidak bisa mengelola user lain
- ❌ Tidak bisa mengubah konfigurasi sistem

---

### 🎪 PENJUAL (EVENT ORGANIZER) ACCOUNTS
**Role:** Penjual Event - Membuat dan mengelola event

| Nama | Email | Password | Deskripsi |
|------|-------|----------|-----------|
| Event Organizer Pro | <EMAIL> | penjual123 | Organizer profesional |
| Maya Event Planner | <EMAIL> | penjual123 | Event planner wanita |
| Rizki Entertainment | <EMAIL> | penjual123 | Entertainment organizer |
| Sinta Creative Tickets | <EMAIL> | penjual123 | Creative event specialist |
| Dedi Music Production | <EMAIL> | penjual123 | Music event producer |

**Hak Akses Penjual:**
- ✅ Membuat event baru
- ✅ Mengelola event milik sendiri
- ✅ Upload poster dan galeri event
- ✅ Melihat statistik penjualan tiket
- ✅ Mengelola harga dan kapasitas
- ✅ Publish/unpublish event
- ❌ Tidak bisa melihat event organizer lain
- ❌ Tidak bisa mengelola user

---

### 🛒 PEMBELI (CUSTOMER) ACCOUNTS
**Role:** Pembeli - Membeli tiket dan menghadiri event

| Nama | Email | Password | Gender | Umur | Lokasi |
|------|-------|----------|---------|------|---------|
| John Doe | <EMAIL> | pembeli123 | Male | 28 | Jakarta Pusat |
| Jane Smith | <EMAIL> | pembeli123 | Female | 30 | Jakarta Selatan |
| Ahmad Fauzi | <EMAIL> | pembeli123 | Male | 31 | Jakarta Pusat |
| Siti Nurhaliza | <EMAIL> | pembeli123 | Female | 29 | Jakarta Selatan |
| Budi Santoso | <EMAIL> | pembeli123 | Male | 27 | Jakarta Timur |
| Dewi Lestari | <EMAIL> | pembeli123 | Female | 26 | Jakarta Timur |
| Rudi Hermawan | <EMAIL> | pembeli123 | Male | 25 | Jakarta Utara |
| Lisa Permata | <EMAIL> | pembeli123 | Female | 24 | Jakarta Utara |

**Hak Akses Pembeli:**
- ✅ Browse dan search event
- ✅ Membeli tiket event
- ✅ Melihat riwayat pembelian
- ✅ Download tiket digital
- ✅ Update profil pribadi
- ✅ Memberikan review event
- ❌ Tidak bisa membuat event
- ❌ Tidak bisa akses dashboard admin

## 📊 Summary

| Role | Jumlah User | Password Default |
|------|-------------|------------------|
| Admin | 2 | TikPro@2024 |
| Staff | 3 | Staff@2024 |
| Penjual | 5 | Penjual@2024 |
| Pembeli | 8 | Pembeli@2024 |
| **Total** | **18** | - |

## 🌐 Akses Aplikasi

- **URL Lokal:** http://localhost/Project-tixara.my.id
- **URL Laragon:** http://project-tixara.my.id.test (jika menggunakan Laragon)

## 🔐 Keamanan

### Password Policy
- Semua password menggunakan format: `{Role}@2024`
- Password di-hash menggunakan Laravel's Hash facade
- Semua akun sudah terverifikasi email (`email_verified_at`)
- Semua akun dalam status aktif (`is_active = true`)

### Rekomendasi Produksi
Untuk environment produksi, pastikan:
1. ❌ **JANGAN** gunakan password default ini
2. ✅ Implementasikan password yang kuat
3. ✅ Aktifkan 2FA untuk admin
4. ✅ Gunakan HTTPS
5. ✅ Implementasikan rate limiting untuk login

## 🧪 Testing Scenarios

### Skenario Testing per Role:

**Admin Testing:**
1. <NAME_EMAIL> / TikPro@2024
2. Akses dashboard admin
3. Kelola user, kategori, dan event
4. Lihat laporan dan statistik

**Staff Testing:**
1. Login sebagai staff (<EMAIL>)
2. Validasi tiket menggunakan QR scanner
3. Bantu customer service
4. Proses refund

**Penjual Testing:**
1. <NAME_EMAIL>
2. Buat event baru
3. Upload poster dan galeri
4. Publish event
5. Monitor penjualan tiket

**Pembeli Testing:**
1. <NAME_EMAIL>
2. Browse event yang tersedia
3. Beli tiket event
4. Download tiket digital
5. Berikan review

## 📝 Notes

- Semua user memiliki data profil lengkap (nama, email, phone, alamat, dll)
- Avatar menggunakan UI Avatars service dengan tema warna hijau (#A8D5BA)
- Tanggal lahir bervariasi untuk testing berbagai rentang umur
- Lokasi tersebar di berbagai area Jakarta
- Last login timestamp di-randomize untuk simulasi aktivitas user

---

**Dibuat oleh:** TiXara Development Team
**Terakhir diupdate:** {{ date('Y-m-d') }}
