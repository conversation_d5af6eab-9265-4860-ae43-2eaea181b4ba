<?php

namespace App\Http\Controllers\Staff;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\Ticket;
use App\Models\TicketValidation;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Show staff dashboard
     */
    public function index()
    {
        $staff = Auth::user();
        $today = Carbon::today();
        
        // Get staff statistics
        $stats = $this->getStaffStats($today);
        
        // Get recent validations
        $recentValidations = $this->getRecentValidations();
        
        // Get today's events
        $todayEvents = $this->getTodayEvents();
        
        return view('pages.staff.dashboard', compact(
            'stats',
            'recentValidations', 
            'todayEvents'
        ));
    }

    /**
     * Get staff statistics
     */
    private function getStaffStats($today)
    {
        $staff = Auth::user();
        
        // Today's validations by this staff
        $todayValidations = TicketValidation::where('validated_by', $staff->id)
            ->whereDate('created_at', $today)
            ->count();
            
        $validTickets = TicketValidation::where('validated_by', $staff->id)
            ->whereDate('created_at', $today)
            ->where('status', 'valid')
            ->count();
            
        $invalidTickets = TicketValidation::where('validated_by', $staff->id)
            ->whereDate('created_at', $today)
            ->where('status', 'invalid')
            ->count();

        // Active events today
        $activeEvents = Event::where('status', 'published')
            ->whereDate('start_date', '<=', $today)
            ->whereDate('end_date', '>=', $today)
            ->count();

        // Upcoming events today
        $upcomingEvents = Event::where('status', 'published')
            ->whereDate('start_date', $today)
            ->where('start_date', '>', now())
            ->count();

        // Performance score calculation
        $totalValidations = $todayValidations;
        $successRate = $totalValidations > 0 ? ($validTickets / $totalValidations) * 100 : 100;
        $performanceScore = min(100, $successRate + ($todayValidations * 2)); // Bonus for volume

        return [
            'today_validations' => $todayValidations,
            'valid_tickets' => $validTickets,
            'invalid_tickets' => $invalidTickets,
            'active_events' => $activeEvents,
            'upcoming_events' => $upcomingEvents,
            'performance_score' => round($performanceScore, 0),
            'success_rate' => round($successRate, 1),
        ];
    }

    /**
     * Get recent validations
     */
    private function getRecentValidations()
    {
        $staff = Auth::user();
        
        return TicketValidation::with(['ticket.event', 'ticket.user'])
            ->where('validated_by', $staff->id)
            ->latest()
            ->limit(10)
            ->get()
            ->map(function ($validation) {
                return [
                    'id' => $validation->id,
                    'ticket_code' => $validation->ticket->ticket_code,
                    'event_title' => $validation->ticket->event->title,
                    'customer_name' => $validation->ticket->user->name,
                    'status' => $validation->status,
                    'validated_at' => $validation->created_at->format('H:i:s'),
                    'created_at' => $validation->created_at,
                ];
            });
    }

    /**
     * Get today's events
     */
    private function getTodayEvents()
    {
        $today = Carbon::today();
        
        return Event::where('status', 'published')
            ->where(function ($query) use ($today) {
                $query->whereDate('start_date', $today)
                      ->orWhere(function ($q) use ($today) {
                          $q->whereDate('start_date', '<=', $today)
                            ->whereDate('end_date', '>=', $today);
                      });
            })
            ->withCount(['tickets as sold_tickets' => function ($query) {
                $query->where('status', '!=', 'cancelled');
            }])
            ->get()
            ->map(function ($event) {
                $now = now();
                $startTime = Carbon::parse($event->start_date);
                $endTime = Carbon::parse($event->end_date);
                
                $status = 'upcoming';
                if ($now->between($startTime, $endTime)) {
                    $status = 'active';
                } elseif ($now->gt($endTime)) {
                    $status = 'ended';
                }
                
                return [
                    'id' => $event->id,
                    'title' => $event->title,
                    'venue_name' => $event->venue_name,
                    'start_time' => $startTime->format('H:i'),
                    'end_time' => $endTime->format('H:i'),
                    'capacity' => $event->total_capacity,
                    'attendees' => $event->sold_tickets,
                    'status' => $status,
                ];
            });
    }

    /**
     * Get dashboard stats API
     */
    public function getStats()
    {
        $today = Carbon::today();
        $stats = $this->getStaffStats($today);
        
        return response()->json($stats);
    }

    /**
     * Get today's events API
     */
    public function getTodayEventsApi()
    {
        $events = $this->getTodayEvents();
        return response()->json($events);
    }

    /**
     * Get validation history API
     */
    public function getValidationHistory(Request $request)
    {
        $staff = Auth::user();
        $limit = $request->get('limit', 20);
        
        $validations = TicketValidation::with(['ticket.event', 'ticket.user'])
            ->where('validated_by', $staff->id)
            ->latest()
            ->limit($limit)
            ->get()
            ->map(function ($validation) {
                return [
                    'id' => $validation->id,
                    'ticket_code' => $validation->ticket->ticket_code,
                    'event_title' => $validation->ticket->event->title,
                    'customer_name' => $validation->ticket->user->name,
                    'status' => $validation->status,
                    'validated_at' => $validation->created_at->diffForHumans(),
                    'created_at' => $validation->created_at,
                ];
            });

        return response()->json($validations);
    }

    /**
     * Validate ticket
     */
    public function validateTicket(Request $request)
    {
        $request->validate([
            'ticket_code' => 'required|string',
        ]);

        $staff = Auth::user();
        $ticketCode = $request->ticket_code;

        // Find ticket
        $ticket = Ticket::where('ticket_code', $ticketCode)
            ->with(['event', 'user'])
            ->first();

        if (!$ticket) {
            return response()->json([
                'success' => false,
                'message' => 'Tiket tidak ditemukan',
                'status' => 'invalid'
            ], 404);
        }

        // Check if ticket is already validated
        $existingValidation = TicketValidation::where('ticket_id', $ticket->id)->first();
        
        if ($existingValidation) {
            return response()->json([
                'success' => false,
                'message' => 'Tiket sudah pernah divalidasi',
                'status' => 'invalid',
                'validated_at' => $existingValidation->created_at->format('d M Y H:i:s'),
                'validated_by' => $existingValidation->validator->name ?? 'Unknown'
            ], 400);
        }

        // Check ticket status
        if ($ticket->status === 'cancelled') {
            return response()->json([
                'success' => false,
                'message' => 'Tiket telah dibatalkan',
                'status' => 'invalid'
            ], 400);
        }

        // Check event date
        $eventDate = Carbon::parse($ticket->event->start_date);
        $today = Carbon::today();
        
        if (!$eventDate->isSameDay($today)) {
            return response()->json([
                'success' => false,
                'message' => 'Tiket tidak valid untuk hari ini',
                'status' => 'invalid',
                'event_date' => $eventDate->format('d M Y')
            ], 400);
        }

        // Validate ticket
        $validation = TicketValidation::create([
            'ticket_id' => $ticket->id,
            'validated_by' => $staff->id,
            'status' => 'valid',
            'validated_at' => now(),
        ]);

        // Update ticket status
        $ticket->update(['status' => 'used']);

        return response()->json([
            'success' => true,
            'message' => 'Tiket berhasil divalidasi',
            'status' => 'valid',
            'ticket' => [
                'code' => $ticket->ticket_code,
                'event' => $ticket->event->title,
                'customer' => $ticket->user->name,
                'validated_at' => $validation->created_at->format('d M Y H:i:s')
            ]
        ]);
    }
}
