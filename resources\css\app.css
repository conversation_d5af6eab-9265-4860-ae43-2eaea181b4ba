@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=DM+Sans:wght@400;500;600;700&display=swap');

/* Custom Theme Colors - TiXara Green Pasta Theme */
:root {
    --color-primary: #A8D5BA;      /* Hijau Pasta */
    --color-primary-50: #f0f9f4;
    --color-primary-100: #dcf2e4;
    --color-primary-200: #bce5cc;
    --color-primary-300: #8dd1a8;
    --color-primary-400: #5bb67d;
    --color-primary-500: #A8D5BA;  /* Main */
    --color-primary-600: #8bc5a0;
    --color-primary-700: #6fa082;
    --color-primary-800: #5a8069;
    --color-primary-900: #4a6856;

    --color-secondary: #F7F7F7;    /* Putih Pucat */
    --color-tertiary: #D6D6D6;     /* Abu Pastel */
    --color-accent: #C7EACB;       /* Hijau Muda */
    --color-dark: #2d3748;         /* Dark Gray */
    --color-light: #F7F7F7;        /* Putih Pucat */

    --color-success: #48bb78;
    --color-warning: #ed8936;
    --color-error: #f56565;
    --color-info: #4299e1;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #A8D5BA 0%, #C7EACB 100%);
    --gradient-secondary: linear-gradient(135deg, #F7F7F7 0%, #D6D6D6 100%);
    --gradient-hero: linear-gradient(135deg, #A8D5BA 0%, #8bc5a0 50%, #6fa082 100%);

    /* Shadows */
    --shadow-soft: 0 2px 15px -3px rgba(168, 213, 186, 0.1), 0 10px 20px -2px rgba(168, 213, 186, 0.04);
    --shadow-medium: 0 4px 25px -5px rgba(168, 213, 186, 0.15), 0 10px 30px -5px rgba(168, 213, 186, 0.08);
    --shadow-large: 0 10px 40px -10px rgba(168, 213, 186, 0.2), 0 20px 50px -10px rgba(168, 213, 186, 0.1);
}

/* Dark Mode Variables */
.dark {
    --color-primary: #A8D5BA;      /* Hijau Pasta tetap sama */
    --color-primary-50: #0f172a;
    --color-primary-100: #1e293b;
    --color-primary-200: #334155;
    --color-primary-300: #475569;
    --color-primary-400: #64748b;
    --color-primary-500: #A8D5BA;  /* Main tetap */
    --color-primary-600: #8bc5a0;
    --color-primary-700: #6fa082;
    --color-primary-800: #5a8069;
    --color-primary-900: #4a6856;

    --color-secondary: #1e293b;    /* Dark Background */
    --color-tertiary: #334155;     /* Dark Gray */
    --color-accent: #C7EACB;       /* Hijau Muda tetap */
    --color-dark: #f8fafc;         /* Light text untuk dark mode */
    --color-light: #0f172a;        /* Dark background */

    /* Dark Mode Gradients */
    --gradient-primary: linear-gradient(135deg, #A8D5BA 0%, #C7EACB 100%);
    --gradient-secondary: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    --gradient-hero: linear-gradient(135deg, #A8D5BA 0%, #8bc5a0 50%, #6fa082 100%);

    /* Dark Mode Shadows */
    --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.3), 0 10px 20px -2px rgba(0, 0, 0, 0.2);
    --shadow-medium: 0 4px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 30px -5px rgba(0, 0, 0, 0.3);
    --shadow-large: 0 10px 40px -10px rgba(0, 0, 0, 0.5), 0 20px 50px -10px rgba(0, 0, 0, 0.4);
}

/* Theme Transition */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Dark Mode Specific Styles */
.dark .card {
    @apply bg-dark-800 border-dark-700;
}

.dark .nav-link {
    @apply text-gray-300 hover:bg-dark-700;
}

.dark .nav-link.active {
    @apply bg-primary-900 text-primary-300;
}

.dark .form-input {
    @apply bg-dark-800 border-dark-600 text-gray-200;
}

.dark .floating-footer {
    @apply bg-dark-800 border-t border-dark-700;
}

.dark .floating-footer-link {
    @apply text-gray-400;
}

.dark .floating-footer-link.active {
    @apply text-primary-400;
}

/* Base Styles */
@layer base {
    body {
        font-family: 'Poppins', 'DM Sans', sans-serif;
        background-color: var(--color-light);
        color: var(--color-dark);
        line-height: 1.6;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    html {
        scroll-behavior: smooth;
    }

    * {
        box-sizing: border-box;
    }
}

/* Custom Components */
@layer components {
    /* Modern Button System */
    .btn {
        @apply inline-flex items-center justify-center px-6 py-3 rounded-xl font-semibold text-sm transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
    }

    .btn-primary {
        @apply bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg hover:shadow-xl focus:ring-primary-300;
        background: var(--gradient-primary);
        box-shadow: var(--shadow-soft);
    }

    .btn-primary:hover {
        box-shadow: var(--shadow-medium);
        transform: translateY(-2px) scale(1.02);
    }

    .btn-secondary {
        @apply bg-white border-2 border-gray-200 text-gray-700 hover:border-primary-300 hover:text-primary-600 focus:ring-primary-200;
    }

    .btn-outline {
        @apply border-2 border-primary-500 text-primary-600 bg-transparent hover:bg-primary-500 hover:text-white focus:ring-primary-300;
    }

    .btn-ghost {
        @apply text-primary-600 bg-primary-50 hover:bg-primary-100 focus:ring-primary-200;
    }

    .btn-sm {
        @apply px-4 py-2 text-xs;
    }

    .btn-lg {
        @apply px-8 py-4 text-base;
    }

    .btn-xl {
        @apply px-10 py-5 text-lg;
    }

    /* Cards */
    .card {
        @apply bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300;
    }

    .card-header {
        @apply p-4 border-b border-gray-100;
    }

    .card-body {
        @apply p-4;
    }

    /* Form Elements */
    .form-input {
        @apply w-full border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500;
    }

    .form-label {
        @apply block text-sm font-medium text-gray-700 mb-1;
    }

    /* Badges */
    .badge {
        @apply px-2 py-1 text-xs rounded-full;
    }

    .badge-primary {
        @apply bg-primary-100 text-primary-600;
    }

    .badge-success {
        @apply bg-green-100 text-green-700;
    }

    .badge-warning {
        @apply bg-yellow-100 text-yellow-700;
    }

    .badge-danger {
        @apply bg-red-100 text-red-700;
    }

    /* Navigation */
    .nav-link {
        @apply flex items-center space-x-2 px-4 py-2 rounded-lg text-gray-600 hover:bg-gray-50 transition-colors;
    }

    .nav-link.active {
        @apply bg-primary-100 text-primary-600;
    }

    /* Floating Footer */
    .floating-footer {
        @apply fixed bottom-0 left-0 right-0 bg-white shadow-lg md:hidden;
    }

    .floating-footer-link {
        @apply flex flex-col items-center space-y-1 text-gray-600;
    }

    .floating-footer-link.active {
        @apply text-primary-600;
    }

    /* Animations */
    .fade-enter {
        @apply opacity-0;
    }

    .fade-enter-active {
        @apply opacity-100 transition-opacity duration-300;
    }

    .fade-exit {
        @apply opacity-100;
    }

    .fade-exit-active {
        @apply opacity-0 transition-opacity duration-300;
    }

    /* Loading States */
    .loading-overlay {
        @apply absolute inset-0 bg-white/80 flex items-center justify-center;
    }

    .loading-spinner {
        @apply animate-spin rounded-full h-8 w-8 border-4 border-primary-500 border-t-transparent;
    }

    /* QR Code Container */
    .qr-container {
        @apply p-4 bg-white rounded-lg shadow-sm;
    }

    /* Event Card */
    .event-card {
        @apply bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300;
    }

    .event-card-image {
        @apply w-full h-48 object-cover;
    }

    .event-card-content {
        @apply p-4;
    }

    /* Profile Section */
    .profile-header {
        @apply bg-primary-500 h-32;
    }

    .profile-avatar {
        @apply w-32 h-32 rounded-full border-4 border-white bg-white object-cover -mt-16;
    }

    /* Admin Dashboard */
    .stat-card {
        @apply bg-white rounded-xl shadow-sm p-6;
    }

    .stat-icon {
        @apply p-3 rounded-lg;
    }

    /* Responsive Tables */
    .table-responsive {
        @apply min-w-full divide-y divide-gray-200;
    }

    .table-header {
        @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
    }

    .table-cell {
        @apply px-6 py-4 whitespace-nowrap;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    @apply w-2;
}

::-webkit-scrollbar-track {
    @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
    @apply bg-primary-300 rounded-full hover:bg-primary-400;
}

/* Typography */
body {
    @apply font-sans text-gray-800;
}

h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .container {
        @apply px-4;
    }

    .main-content {
        @apply pb-20; /* Space for floating footer */
    }
}

/* Print Styles */
@media print {
    .no-print {
        @apply hidden;
    }

    .print-only {
        @apply block;
    }
}