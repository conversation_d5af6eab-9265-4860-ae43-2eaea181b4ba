<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Category;
use App\Models\Event;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class TiXaraSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Admin User
        $admin = User::create([
            'name' => 'Admin TikPro',
            'email' => '<EMAIL>',
            'phone' => '081234567890',
            'password' => Hash::make('TikPro@2024'),
            'role' => User::ROLE_ADMIN,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create Staff User
        $staff = User::create([
            'name' => 'Staff TikPro',
            'email' => '<EMAIL>',
            'phone' => '081234567891',
            'password' => Hash::make('Staff@2024'),
            'role' => User::ROLE_STAFF,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create Organizer Users
        $organizer1 = User::create([
            'name' => 'Event Organizer 1',
            'email' => '<EMAIL>',
            'phone' => '081234567892',
            'password' => Hash::make('Penjual@2024'),
            'role' => User::ROLE_PENJUAL,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $organizer2 = User::create([
            'name' => 'Event Organizer 2',
            'email' => '<EMAIL>',
            'phone' => '081234567893',
            'password' => Hash::make('Penjual@2024'),
            'role' => User::ROLE_PENJUAL,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create Regular Users
        $user1 = User::create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '081234567894',
            'password' => Hash::make('Pembeli@2024'),
            'role' => User::ROLE_PEMBELI,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $user2 = User::create([
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'phone' => '081234567895',
            'password' => Hash::make('Pembeli@2024'),
            'role' => User::ROLE_PEMBELI,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create Categories
        $categories = [
            [
                'name' => 'Konser',
                'slug' => 'konser',
                'description' => 'Konser musik dari berbagai genre',
                'icon' => 'fas fa-music',
                'is_active' => true,
            ],
            [
                'name' => 'Festival',
                'slug' => 'festival',
                'description' => 'Festival musik dan budaya',
                'icon' => 'fas fa-calendar-alt',
                'is_active' => true,
            ],
            [
                'name' => 'Seminar',
                'slug' => 'seminar',
                'description' => 'Seminar dan workshop edukatif',
                'icon' => 'fas fa-chalkboard-teacher',
                'is_active' => true,
            ],
            [
                'name' => 'Cinema',
                'slug' => 'cinema',
                'description' => 'Pemutaran film dan bioskop',
                'icon' => 'fas fa-film',
                'is_active' => true,
            ],
            [
                'name' => 'Olahraga',
                'slug' => 'olahraga',
                'description' => 'Event olahraga dan kompetisi',
                'icon' => 'fas fa-futbol',
                'is_active' => true,
            ],
            [
                'name' => 'Workshop',
                'slug' => 'workshop',
                'description' => 'Workshop kreatif dan skill development',
                'icon' => 'fas fa-tools',
                'is_active' => true,
            ],
            [
                'name' => 'Stand Up Comedy',
                'slug' => 'stand-up-comedy',
                'description' => 'Pertunjukan komedi dan hiburan',
                'icon' => 'fas fa-laugh',
                'is_active' => true,
            ],
            [
                'name' => 'Theater',
                'slug' => 'theater',
                'description' => 'Pertunjukan teater dan drama',
                'icon' => 'fas fa-theater-masks',
                'is_active' => true,
            ],
        ];

        foreach ($categories as $categoryData) {
            Category::create($categoryData);
        }

        // Get created categories
        $konserCategory = Category::where('slug', 'konser')->first();
        $festivalCategory = Category::where('slug', 'festival')->first();
        $seminarCategory = Category::where('slug', 'seminar')->first();
        $cinemaCategory = Category::where('slug', 'cinema')->first();
        $olahragaCategory = Category::where('slug', 'olahraga')->first();
        $workshopCategory = Category::where('slug', 'workshop')->first();
        $comedyCategory = Category::where('slug', 'stand-up-comedy')->first();
        $theaterCategory = Category::where('slug', 'theater')->first();

        // Create Sample Tickets
        $tickets = [
            [
                'title' => 'Konser Raisa Live in Jakarta',
                'slug' => 'konser-raisa-live-in-jakarta',
                'description' => 'Konser spektakuler Raisa dengan lagu-lagu terbaiknya. Jangan lewatkan kesempatan untuk menyaksikan penampilan memukau dari salah satu penyanyi terbaik Indonesia.',
                'category_id' => $konserCategory->id,
                'organizer_id' => $organizer1->id,
                'venue_name' => 'Jakarta Convention Center',
                'venue_address' => 'Jl. Gatot Subroto, Jakarta Pusat',
                'city' => 'Jakarta',
                'province' => 'DKI Jakarta',
                'latitude' => -6.2088,
                'longitude' => 106.8456,
                'start_date' => Carbon::now()->addDays(30),
                'end_date' => Carbon::now()->addDays(30)->addHours(3),
                'price' => 350000,
                'total_capacity' => 5000,
                'available_capacity' => 5000,
                'status' => 'published',
                'is_featured' => true,
                'poster' => 'https://via.placeholder.com/400x600/A8D5BA/FFFFFF?text=Konser+Raisa',
            ],
            [
                'title' => 'Java Jazz Festival 2024',
                'slug' => 'java-jazz-festival-2024',
                'description' => 'Festival jazz terbesar di Asia Tenggara dengan lineup internasional dan lokal terbaik. Nikmati 3 hari penuh musik jazz yang memukau.',
                'category_id' => $festivalCategory->id,
                'organizer_id' => $organizer2->id,
                'venue_name' => 'JIExpo Kemayoran',
                'venue_address' => 'Jl. Boulevard Barat Raya No.1, Jakarta Utara',
                'city' => 'Jakarta',
                'province' => 'DKI Jakarta',
                'latitude' => -6.1477,
                'longitude' => 106.8820,
                'start_date' => Carbon::now()->addDays(45),
                'end_date' => Carbon::now()->addDays(47),
                'price' => 750000,
                'total_capacity' => 15000,
                'available_capacity' => 15000,
                'status' => 'published',
                'is_featured' => true,
                'poster' => 'https://via.placeholder.com/400x600/A8D5BA/FFFFFF?text=Java+Jazz+Festival',
            ],
            [
                'title' => 'Seminar Digital Marketing 2024',
                'slug' => 'seminar-digital-marketing-2024',
                'description' => 'Pelajari strategi digital marketing terkini dari para ahli. Cocok untuk entrepreneur dan marketer yang ingin meningkatkan skill.',
                'category_id' => $seminarCategory->id,
                'organizer_id' => $organizer1->id,
                'venue_name' => 'Hotel Mulia Senayan',
                'venue_address' => 'Jl. Asia Afrika Senayan, Jakarta Pusat',
                'city' => 'Jakarta',
                'province' => 'DKI Jakarta',
                'latitude' => -6.2297,
                'longitude' => 106.8075,
                'start_date' => Carbon::now()->addDays(15),
                'end_date' => Carbon::now()->addDays(15)->addHours(8),
                'price' => 150000,
                'total_capacity' => 500,
                'available_capacity' => 500,
                'status' => 'published',
                'is_featured' => false,
                'poster' => 'https://via.placeholder.com/400x600/A8D5BA/FFFFFF?text=Digital+Marketing+Seminar',
            ],
            [
                'title' => 'Premiere Film Laskar Pelangi 2',
                'slug' => 'premiere-film-laskar-pelangi-2',
                'description' => 'Premiere eksklusif film Laskar Pelangi 2 dengan kehadiran para pemain dan sutradara. Saksikan kelanjutan kisah inspiratif ini.',
                'category_id' => $cinemaCategory->id,
                'organizer_id' => $organizer2->id,
                'venue_name' => 'CGV Grand Indonesia',
                'venue_address' => 'Jl. MH Thamrin No.1, Jakarta Pusat',
                'city' => 'Jakarta',
                'province' => 'DKI Jakarta',
                'latitude' => -6.1944,
                'longitude' => 106.8229,
                'start_date' => Carbon::now()->addDays(20),
                'end_date' => Carbon::now()->addDays(20)->addHours(3),
                'price' => 100000,
                'total_capacity' => 200,
                'available_capacity' => 200,
                'status' => 'published',
                'is_featured' => false,
                'poster' => 'https://via.placeholder.com/400x600/A8D5BA/FFFFFF?text=Laskar+Pelangi+2',
            ],
            [
                'title' => 'Pertandingan Persija vs Persib',
                'slug' => 'pertandingan-persija-vs-persib',
                'description' => 'El Clasico Indonesia! Saksikan pertandingan seru antara Persija Jakarta melawan Persib Bandung di Stadion Utama GBK.',
                'category_id' => $olahragaCategory->id,
                'organizer_id' => $organizer1->id,
                'venue_name' => 'Stadion Utama Gelora Bung Karno',
                'venue_address' => 'Jl. Pintu Satu Senayan, Jakarta Pusat',
                'city' => 'Jakarta',
                'province' => 'DKI Jakarta',
                'latitude' => -6.2182,
                'longitude' => 106.8019,
                'start_date' => Carbon::now()->addDays(25),
                'end_date' => Carbon::now()->addDays(25)->addHours(2),
                'price' => 75000,
                'total_capacity' => 80000,
                'available_capacity' => 80000,
                'status' => 'published',
                'is_featured' => true,
                'poster' => 'https://via.placeholder.com/400x600/A8D5BA/FFFFFF?text=Persija+vs+Persib',
            ],
        ];

        foreach ($tickets as $eventData) {
            Event::create($eventData);
        }

        // Create sample notifications
        $this->call(NotificationSeeder::class);

        $this->command->info('TikPro seeder completed successfully!');
        $this->command->info('');
        $this->command->info('=== LOGIN CREDENTIALS ===');
        $this->command->info('Admin: <EMAIL> / TikPro@2024');
        $this->command->info('Staff: <EMAIL> / Staff@2024');
        $this->command->info('Organizer: <EMAIL> / Penjual@2024');
        $this->command->info('Customer: <EMAIL> / Pembeli@2024');
    }
}
