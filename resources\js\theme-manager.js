/**
 * TiXara Theme Manager
 * Manages dynamic theme switching and persistence across the application
 */

class ThemeManager {
    constructor() {
        this.themes = {
            'green': { 
                primary: '#A8D5BA', 
                secondary: '#E8F5E8', 
                accent: '#7FB069',
                name: '<PERSON> Pasta'
            },
            'blue': { 
                primary: '#93C5FD', 
                secondary: '#DBEAFE', 
                accent: '#3B82F6',
                name: 'Ocean Blue'
            },
            'purple': { 
                primary: '#C4B5FD', 
                secondary: '#EDE9FE', 
                accent: '#8B5CF6',
                name: '<PERSON> Purple'
            },
            'pink': { 
                primary: '#F9A8D4', 
                secondary: '#FCE7F3', 
                accent: '#EC4899',
                name: '<PERSON>'
            },
            'orange': { 
                primary: '#FDBA74', 
                secondary: '#FED7AA', 
                accent: '#F97316',
                name: 'Sunset Orange'
            }
        };
        
        this.currentTheme = localStorage.getItem('themeColor') || 'green';
        this.darkMode = localStorage.getItem('darkMode') === 'true' || 
                       (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches);
        
        this.init();
    }
    
    init() {
        this.applyTheme();
        this.setupEventListeners();
        this.setupSystemThemeListener();
        this.initializeNotifications();
    }
    
    applyTheme() {
        const root = document.documentElement;
        const theme = this.themes[this.currentTheme];
        
        // Apply dark mode class
        if (this.darkMode) {
            root.classList.add('dark');
        } else {
            root.classList.remove('dark');
        }
        
        // Apply theme colors as CSS custom properties
        root.style.setProperty('--color-primary', theme.primary);
        root.style.setProperty('--color-secondary', theme.secondary);
        root.style.setProperty('--color-accent', theme.accent);
        root.style.setProperty('--theme-primary', theme.primary);
        root.style.setProperty('--theme-secondary', theme.secondary);
        root.style.setProperty('--theme-accent', theme.accent);
        
        // Convert hex to RGB for rgba usage
        const primaryRGB = this.hexToRgb(theme.primary);
        const secondaryRGB = this.hexToRgb(theme.secondary);
        const accentRGB = this.hexToRgb(theme.accent);
        
        if (primaryRGB) {
            root.style.setProperty('--theme-primary-rgb', `${primaryRGB.r}, ${primaryRGB.g}, ${primaryRGB.b}`);
        }
        if (secondaryRGB) {
            root.style.setProperty('--theme-secondary-rgb', `${secondaryRGB.r}, ${secondaryRGB.g}, ${secondaryRGB.b}`);
        }
        if (accentRGB) {
            root.style.setProperty('--theme-accent-rgb', `${accentRGB.r}, ${accentRGB.g}, ${accentRGB.b}`);
        }
        
        // Update meta theme color for mobile browsers
        this.updateMetaThemeColor(theme.primary);
        
        // Dispatch theme change event
        this.dispatchThemeChangeEvent();
        
        // Save to localStorage
        localStorage.setItem('themeColor', this.currentTheme);
        localStorage.setItem('darkMode', this.darkMode);
        
        // Update notification badge color
        this.updateNotificationBadge();
        
        console.log(`Theme applied: ${theme.name} (${this.darkMode ? 'Dark' : 'Light'} mode)`);
    }
    
    toggleDarkMode() {
        this.darkMode = !this.darkMode;
        this.applyTheme();
        this.showThemeChangeNotification();
    }
    
    changeTheme(themeColor) {
        if (this.themes[themeColor]) {
            this.currentTheme = themeColor;
            this.applyTheme();
            this.showThemeChangeNotification();
        }
    }
    
    setupEventListeners() {
        // Listen for theme change events from Alpine.js components
        window.addEventListener('themeChanged', (event) => {
            const { darkMode, themeColor } = event.detail;
            this.darkMode = darkMode;
            this.currentTheme = themeColor;
            this.applyTheme();
        });
        
        // Listen for keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            // Ctrl/Cmd + Shift + D for dark mode toggle
            if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'D') {
                event.preventDefault();
                this.toggleDarkMode();
            }
            
            // Ctrl/Cmd + Shift + T for theme cycling
            if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'T') {
                event.preventDefault();
                this.cycleTheme();
            }
        });
    }
    
    setupSystemThemeListener() {
        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('darkMode')) {
                this.darkMode = e.matches;
                this.applyTheme();
            }
        });
    }
    
    cycleTheme() {
        const themeKeys = Object.keys(this.themes);
        const currentIndex = themeKeys.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % themeKeys.length;
        this.changeTheme(themeKeys[nextIndex]);
    }
    
    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }
    
    updateMetaThemeColor(color) {
        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }
        metaThemeColor.content = color;
    }
    
    dispatchThemeChangeEvent() {
        window.dispatchEvent(new CustomEvent('themeApplied', {
            detail: {
                theme: this.themes[this.currentTheme],
                themeColor: this.currentTheme,
                darkMode: this.darkMode
            }
        }));
    }
    
    showThemeChangeNotification() {
        const theme = this.themes[this.currentTheme];
        const message = `Tema diubah ke ${theme.name} (${this.darkMode ? 'Mode Gelap' : 'Mode Terang'})`;
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `
            fixed top-20 right-4 z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 
            rounded-lg shadow-lg p-4 transform translate-x-full transition-transform duration-300 ease-out
        `;
        notification.innerHTML = `
            <div class="flex items-center space-x-3">
                <div class="w-4 h-4 rounded-full" style="background-color: ${theme.primary}"></div>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">${message}</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Animate out and remove
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    initializeNotifications() {
        // Initialize notification system with theme colors
        this.updateNotificationBadge();
        
        // Setup notification sound
        this.notificationSound = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
    }
    
    updateNotificationBadge() {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            const theme = this.themes[this.currentTheme];
            badge.style.background = `linear-gradient(135deg, ${theme.accent}, ${theme.primary})`;
        }
    }
    
    playNotificationSound() {
        if (this.notificationSound && !document.hidden) {
            this.notificationSound.currentTime = 0;
            this.notificationSound.play().catch(() => {
                // Ignore autoplay restrictions
            });
        }
    }
    
    // Public API methods
    getCurrentTheme() {
        return {
            color: this.currentTheme,
            theme: this.themes[this.currentTheme],
            darkMode: this.darkMode
        };
    }
    
    getAvailableThemes() {
        return this.themes;
    }
    
    isDarkMode() {
        return this.darkMode;
    }
    
    getThemeColor() {
        return this.currentTheme;
    }
}

// Initialize theme manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.themeManager = new ThemeManager();
    
    // Make theme manager globally accessible
    window.TiXara = window.TiXara || {};
    window.TiXara.themeManager = window.themeManager;
    
    // Expose methods for Alpine.js components
    window.toggleDarkMode = () => window.themeManager.toggleDarkMode();
    window.changeTheme = (color) => window.themeManager.changeTheme(color);
    window.getCurrentTheme = () => window.themeManager.getCurrentTheme();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}
