<?php

namespace App\Http\Controllers;

use App\Models\Event;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class HomeController extends Controller
{
    /**
     * Show the home page
     */
    public function index()
    {
        // Cache data for better performance
        $categories = Cache::remember('home_categories', 3600, function () {
            return Category::active()
                          ->ordered()
                          ->withCount(['tickets as events_count' => function ($query) {
                              $query->where('status', 'published')
                                    ->where('start_date', '>', now());
                          }])
                          ->limit(8)
                          ->get();
        });

        // Get trending categories
        $trendingCategories = Cache::remember('trending_categories_home', 1800, function () {
            return Category::select('categories.*')
                ->join('events', 'categories.id', '=', 'events.category_id')
                ->join('orders', 'events.id', '=', 'orders.event_id')
                ->where('orders.created_at', '>=', now()->subDays(7))
                ->where('orders.payment_status', 'paid')
                ->groupBy('categories.id')
                ->orderByRaw('COUNT(orders.id) DESC')
                ->limit(4)
                ->get();
        });

        $featuredTickets = Cache::remember('featured_Tickets', 1800, function () {
            return Event::with(['category', 'organizer'])
                       ->where('status', 'published')
                       ->where('is_featured', true)
                       ->where('start_date', '>', now())
                       ->orderBy('start_date')
                       ->limit(6)
                       ->get();
        });

        $latestTickets = Cache::remember('latest_tickets', 1800, function () {
            return Event::with(['category', 'organizer'])
                       ->where('status', 'published')
                       ->where('start_date', '>', now())
                       ->orderBy('created_at', 'desc')
                       ->limit(6)
                       ->get();
        });

        $popularTickets = Cache::remember('popular_tickets', 1800, function () {
            return Event::with(['category', 'organizer'])
                       ->where('status', 'published')
                       ->where('start_date', '>', now())
                       ->withCount('orders')
                       ->orderBy('orders_count', 'desc')
                       ->limit(6)
                       ->get();
        });

        // Get all tickets for the main grid (with pagination)
        $tickets = Event::with(['category', 'organizer'])
                      ->where('status', 'published')
                      ->where('start_date', '>', now())
                      ->orderBy('start_date')
                      ->paginate(12);

        return view('pages.home', [
            'title' => 'Beranda',
            'categories' => $categories,
            'trendingCategories' => $trendingCategories,
            'featuredTickets' => $featuredTickets,
            'latestTickets' => $latestTickets,
            'popularTickets' => $popularTickets,
            'tickets' => $tickets,
        ]);
    }

    /**
     * Get featured tickets for AJAX
     */
    public function featuredTickets()
    {
        $tickets = Event::with(['category', 'organizer'])
                      ->where('status', 'published')
                      ->where('is_featured', true)
                      ->where('start_date', '>', now())
                      ->orderBy('start_date')
                      ->limit(6)
                      ->get()
                      ->map(function ($event) {
                          return [
                              'id' => $event->id,
                              'title' => $event->title,
                              'poster' => $event->poster_url,
                              'category' => $event->category->name,
                              'start_date' => $event->start_date->format('d M Y'),
                              'venue_name' => $event->venue_name,
                              'city' => $event->city,
                              'price' => $event->formatted_price,
                              'url' => route('tickets.show', $event),
                              'is_free' => $event->price == 0,
                              'available_tickets' => $event->available_capacity,
                          ];
                      });

        return response()->json($tickets);
    }

    /**
     * Get latest tickets for AJAX
     */
    public function latestTickets()
    {
        $tickets = Event::with(['category', 'organizer'])
                      ->where('status', 'published')
                      ->where('start_date', '>', now())
                      ->orderBy('created_at', 'desc')
                      ->limit(6)
                      ->get()
                      ->map(function ($event) {
                          return [
                              'id' => $event->id,
                              'title' => $event->title,
                              'poster' => $event->poster_url,
                              'category' => $event->category->name,
                              'start_date' => $event->start_date->format('d M Y'),
                              'venue_name' => $event->venue_name,
                              'city' => $event->city,
                              'price' => $event->formatted_price,
                              'url' => route('tickets.show', $event),
                              'is_free' => $event->price == 0,
                              'available_tickets' => $event->available_capacity,
                          ];
                      });

        return response()->json($tickets);
    }

    /**
     * Get popular tickets for AJAX
     */
    public function popularTickets()
    {
        $tickets = Event::with(['category', 'organizer'])
                      ->where('status', 'published')
                      ->where('start_date', '>', now())
                      ->withCount('orders')
                      ->orderBy('orders_count', 'desc')
                      ->limit(6)
                      ->get()
                      ->map(function ($event) {
                          return [
                              'id' => $event->id,
                              'title' => $event->title,
                              'poster' => $event->poster_url,
                              'category' => $event->category->name,
                              'start_date' => $event->start_date->format('d M Y'),
                              'venue_name' => $event->venue_name,
                              'city' => $event->city,
                              'price' => $event->formatted_price,
                              'url' => route('tickets.show', $event),
                              'is_free' => $event->price == 0,
                              'available_tickets' => $event->available_capacity,
                          ];
                      });

        return response()->json($tickets);
    }

    /**
     * Search tickets
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');
        $category = $request->get('category');
        $city = $request->get('city');
        $date = $request->get('date');
        $price_min = $request->get('price_min');
        $price_max = $request->get('price_max');

        $tickets = Event::with(['category', 'organizer'])
                      ->where('status', 'published')
                      ->where('start_date', '>', now());

        // Search by title or description
        if ($query) {
            $tickets->where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('venue_name', 'like', "%{$query}%");
            });
        }

        // Filter by category
        if ($category) {
            $tickets->where('category_id', $category);
        }

        // Filter by city
        if ($city) {
            $tickets->where('city', 'like', "%{$city}%");
        }

        // Filter by date
        if ($date) {
            $tickets->whereDate('start_date', $date);
        }

        // Filter by price range
        if ($price_min) {
            $tickets->where('price', '>=', $price_min);
        }

        if ($price_max) {
            $tickets->where('price', '<=', $price_max);
        }

        $tickets = $tickets->orderBy('start_date')->paginate(12);

        $categories = Category::where('is_active', true)->orderBy('name')->get();

        return view('pages.search', [
            'title' => 'Pencarian Tickets',
            'tickets' => $tickets,
            'categories' => $categories,
            'query' => $query,
            'filters' => [
                'category' => $category,
                'city' => $city,
                'date' => $date,
                'price_min' => $price_min,
                'price_max' => $price_max,
            ]
        ]);
    }

    /**
     * Get tickets by category
     */
    public function ticketsByCategory(Category $category)
    {
        $tickets = Event::with(['category', 'organizer'])
                      ->where('category_id', $category->id)
                      ->where('status', 'published')
                      ->where('start_date', '>', now())
                      ->orderBy('start_date')
                      ->paginate(12);

        return view('pages.category', [
            'title' => "Tiket {$category->name}",
            'category' => $category,
            'tickets' => $tickets,
        ]);
    }

    /**
     * Get nearby tickets based on user location
     */
    public function nearby(Request $request)
    {
        $latitude = $request->get('lat');
        $longitude = $request->get('lng');
        $radius = $request->get('radius', 50); // Default 50km

        if (!$latitude || !$longitude) {
            return response()->json(['error' => 'Location required'], 400);
        }

        // Using Haversine formula to calculate distance
        $tickets = Event::with(['category', 'organizer'])
                      ->where('status', 'published')
                      ->where('start_date', '>', now())
                      ->whereNotNull('latitude')
                      ->whereNotNull('longitude')
                      ->selectRaw("
                          *,
                          (6371 * acos(cos(radians(?))
                          * cos(radians(latitude))
                          * cos(radians(longitude) - radians(?))
                          + sin(radians(?))
                          * sin(radians(latitude)))) AS distance
                      ", [$latitude, $longitude, $latitude])
                      ->having('distance', '<', $radius)
                      ->orderBy('distance')
                      ->limit(20)
                      ->get();

        return response()->json($tickets->map(function ($event) {
            return [
                'id' => $event->id,
                'title' => $event->title,
                'poster' => $event->poster_url,
                'category' => $event->category->name,
                'start_date' => $event->start_date->format('d M Y'),
                'venue_name' => $event->venue_name,
                'city' => $event->city,
                'price' => $event->formatted_price,
                'url' => route('tickets.show', $event),
                'distance' => round($event->distance, 1),
            ];
        }));
    }
}
