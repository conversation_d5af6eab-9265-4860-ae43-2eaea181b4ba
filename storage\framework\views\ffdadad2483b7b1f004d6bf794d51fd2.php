

<?php $__env->startSection('title', 'Admin Dashboard - TikPro'); ?>

<?php $__env->startPush('styles'); ?>
<style>
.chart-container {
    position: relative;
    height: 300px;
}

.metric-card {
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    transition: width 0.8s ease-in-out;
}

.animate-counter {
    animation: countUp 1.5s ease-out;
}

@keyframes countUp {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.status-indicator {
    position: relative;
}

.status-indicator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -8px;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: currentColor;
    transform: translateY(-50%);
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Dashboard Header -->
    <div class="mb-8" data-aos="fade-up">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">👑 Admin Dashboard</h1>
                <p class="text-gray-600">Kelola platform TikPro dengan kontrol penuh dan analitik mendalam</p>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-3">
                <!-- Date Range Filter -->
                <select id="dateRange" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    <option value="7" <?php echo e($dateRange == 7 ? 'selected' : ''); ?>>7 Hari Terakhir</option>
                    <option value="30" <?php echo e($dateRange == 30 ? 'selected' : ''); ?>>30 Hari Terakhir</option>
                    <option value="90" <?php echo e($dateRange == 90 ? 'selected' : ''); ?>>90 Hari Terakhir</option>
                    <option value="365" <?php echo e($dateRange == 365 ? 'selected' : ''); ?>>1 Tahun Terakhir</option>
                </select>

                <!-- Refresh Button -->
                <button onclick="refreshDashboard()" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                    <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                </button>

                <!-- Export Button -->
                <button onclick="exportReport()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                    Export
                </button>
            </div>
        </div>
    </div>

    <!-- System Health Alert -->
    <?php if(isset($systemHealth) && $systemHealth['status'] !== 'healthy'): ?>
    <div class="mb-6 bg-yellow-50 border border-yellow-200 rounded-xl p-4" data-aos="fade-up">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i data-lucide="alert-triangle" class="w-5 h-5 text-yellow-600"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">System Health Warning</h3>
                <p class="text-sm text-yellow-700 mt-1"><?php echo e($systemHealth['message'] ?? 'Some system components need attention.'); ?></p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Enhanced Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Events -->
        <div class="metric-card bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg p-6 text-white" data-aos="fade-up" data-aos-delay="100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-white/20 rounded-lg backdrop-blur-sm">
                    <i data-lucide="calendar" class="w-6 h-6"></i>
                </div>
                <div class="text-right">
                    <p class="text-blue-100 text-sm">Total Events</p>
                    <p class="text-3xl font-bold animate-counter" data-target="<?php echo e($platformStats['total_tickets'] ?? 0); ?>">0</p>
                </div>
            </div>
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center">
                    <div class="w-2 h-2 bg-green-400 rounded-full mr-2 pulse"></div>
                    <span class="text-blue-100"><?php echo e(number_format($platformStats['published_tickets'] ?? 0, 0, ',', '.')); ?> Published</span>
                </div>
                <span class="text-blue-200">+<?php echo e(number_format($platformStats['new_tickets'] ?? 0, 0, ',', '.')); ?> Baru</span>
            </div>
            <div class="mt-4">
                <div class="bg-white/20 rounded-full h-2">
                    <div class="progress-bar bg-white rounded-full h-2" style="width: <?php echo e($platformStats['total_tickets'] > 0 ? ($platformStats['published_tickets'] / $platformStats['total_tickets']) * 100 : 0); ?>%"></div>
                </div>
                <p class="text-xs text-blue-100 mt-1"><?php echo e($platformStats['total_tickets'] > 0 ? round(($platformStats['published_tickets'] / $platformStats['total_tickets']) * 100, 1) : 0); ?>% Published</p>
            </div>
        </div>

        <!-- Total Users -->
        <div class="metric-card bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg p-6 text-white" data-aos="fade-up" data-aos-delay="200">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-white/20 rounded-lg backdrop-blur-sm">
                    <i data-lucide="users" class="w-6 h-6"></i>
                </div>
                <div class="text-right">
                    <p class="text-green-100 text-sm">Total Users</p>
                    <p class="text-3xl font-bold animate-counter" data-target="<?php echo e($platformStats['total_users'] ?? 0); ?>">0</p>
                </div>
            </div>
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center">
                    <div class="w-2 h-2 bg-yellow-400 rounded-full mr-2 pulse"></div>
                    <span class="text-green-100"><?php echo e(number_format($platformStats['total_organizers'] ?? 0, 0, ',', '.')); ?> Organizers</span>
                </div>
                <span class="text-green-200">+<?php echo e(number_format($platformStats['new_users'] ?? 0, 0, ',', '.')); ?> Baru</span>
            </div>
            <div class="mt-4">
                <div class="bg-white/20 rounded-full h-2">
                    <div class="progress-bar bg-white rounded-full h-2" style="width: <?php echo e($platformStats['total_users'] > 0 ? ($platformStats['active_organizers'] / $platformStats['total_organizers']) * 100 : 0); ?>%"></div>
                </div>
                <p class="text-xs text-green-100 mt-1"><?php echo e($platformStats['total_organizers'] > 0 ? round(($platformStats['active_organizers'] / $platformStats['total_organizers']) * 100, 1) : 0); ?>% Active Organizers</p>
            </div>
        </div>

        <!-- Total Revenue -->
        <div class="metric-card bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg p-6 text-white" data-aos="fade-up" data-aos-delay="300">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-white/20 rounded-lg backdrop-blur-sm">
                    <i data-lucide="dollar-sign" class="w-6 h-6"></i>
                </div>
                <div class="text-right">
                    <p class="text-purple-100 text-sm">Total Revenue</p>
                    <p class="text-2xl font-bold">Rp <?php echo e(number_format($platformStats['total_revenue'] ?? 0, 0, ',', '.')); ?></p>
                </div>
            </div>
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center">
                    <div class="w-2 h-2 bg-green-400 rounded-full mr-2 pulse"></div>
                    <span class="text-purple-100">Platform Fee</span>
                </div>
                <span class="text-purple-200">+Rp <?php echo e(number_format($platformStats['period_revenue'] ?? 0, 0, ',', '.')); ?></span>
            </div>
            <div class="mt-4">
                <?php
                    $growthRate = $revenueAnalytics['growth_rate'] ?? 0;
                    $isPositive = $growthRate >= 0;
                ?>
                <div class="flex items-center">
                    <i data-lucide="<?php echo e($isPositive ? 'trending-up' : 'trending-down'); ?>" class="w-4 h-4 mr-1 <?php echo e($isPositive ? 'text-green-300' : 'text-red-300'); ?>"></i>
                    <span class="text-xs <?php echo e($isPositive ? 'text-green-300' : 'text-red-300'); ?>">
                        <?php echo e($isPositive ? '+' : ''); ?><?php echo e(number_format($growthRate, 1)); ?>% vs periode sebelumnya
                    </span>
                </div>
            </div>
        </div>

        <!-- Tickets Sold -->
        <div class="metric-card bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg p-6 text-white" data-aos="fade-up" data-aos-delay="400">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-white/20 rounded-lg backdrop-blur-sm">
                    <i data-lucide="ticket" class="w-6 h-6"></i>
                </div>
                <div class="text-right">
                    <p class="text-orange-100 text-sm">Tickets Sold</p>
                    <p class="text-3xl font-bold animate-counter" data-target="<?php echo e($platformStats['total_tickets_sold'] ?? 0); ?>">0</p>
                </div>
            </div>
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center">
                    <div class="w-2 h-2 bg-blue-400 rounded-full mr-2 pulse"></div>
                    <span class="text-orange-100"><?php echo e($dateRange); ?> hari terakhir</span>
                </div>
                <span class="text-orange-200">+<?php echo e(number_format($platformStats['period_tickets_sold'] ?? 0, 0, ',', '.')); ?></span>
            </div>
            <div class="mt-4">
                <div class="flex items-center">
                    <i data-lucide="activity" class="w-4 h-4 mr-1 text-orange-300"></i>
                    <span class="text-xs text-orange-300">
                        Avg: <?php echo e($platformStats['total_tickets'] > 0 ? round($platformStats['total_tickets_sold'] / $platformStats['total_tickets'], 1) : 0); ?> per event
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-8" data-aos="fade-up" data-aos-delay="500">
        <a href="<?php echo e(route('admin.tickets.create')); ?>" class="flex flex-col items-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 group">
            <div class="p-3 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">
                <i data-lucide="plus" class="w-6 h-6 text-blue-600"></i>
            </div>
            <span class="text-sm font-medium text-gray-700 mt-2">Add Event</span>
        </a>

        <a href="<?php echo e(route('admin.users.index')); ?>" class="flex flex-col items-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 group">
            <div class="p-3 bg-green-100 rounded-lg group-hover:bg-green-200 transition-colors">
                <i data-lucide="user-plus" class="w-6 h-6 text-green-600"></i>
            </div>
            <span class="text-sm font-medium text-gray-700 mt-2">Manage Users</span>
        </a>

        <a href="<?php echo e(route('admin.payments.index')); ?>" class="flex flex-col items-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 group">
            <div class="p-3 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors">
                <i data-lucide="credit-card" class="w-6 h-6 text-purple-600"></i>
            </div>
            <span class="text-sm font-medium text-gray-700 mt-2">Payments</span>
        </a>

        <a href="<?php echo e(route('admin.notifications.index')); ?>" class="flex flex-col items-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 group">
            <div class="p-3 bg-yellow-100 rounded-lg group-hover:bg-yellow-200 transition-colors">
                <i data-lucide="bell" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <span class="text-sm font-medium text-gray-700 mt-2">Notifications</span>
        </a>

        <a href="<?php echo e(route('admin.organizers.index')); ?>" class="flex flex-col items-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 group">
            <div class="p-3 bg-indigo-100 rounded-lg group-hover:bg-indigo-200 transition-colors">
                <i data-lucide="briefcase" class="w-6 h-6 text-indigo-600"></i>
            </div>
            <span class="text-sm font-medium text-gray-700 mt-2">Organizers</span>
        </a>

        <button onclick="showReportsModal()" class="flex flex-col items-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 group">
            <div class="p-3 bg-pink-100 rounded-lg group-hover:bg-pink-200 transition-colors">
                <i data-lucide="bar-chart-3" class="w-6 h-6 text-pink-600"></i>
            </div>
            <span class="text-sm font-medium text-gray-700 mt-2">Reports</span>
        </button>
    </div>

    <!-- Analytics Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Revenue Chart -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="600">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">Revenue Analytics</h3>
                <div class="flex space-x-2">
                    <button onclick="changeChartPeriod('revenue', 'daily')" class="chart-period-btn px-3 py-1 text-sm rounded-lg bg-primary text-white">Daily</button>
                    <button onclick="changeChartPeriod('revenue', 'monthly')" class="chart-period-btn px-3 py-1 text-sm rounded-lg text-gray-600 hover:bg-gray-100">Monthly</button>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="revenueChart"></canvas>
            </div>
        </div>

        <!-- User Growth Chart -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="700">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">User Growth</h3>
                <div class="flex items-center space-x-2">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                        <span class="text-sm text-gray-600">New Users</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                        <span class="text-sm text-gray-600">Organizers</span>
                    </div>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="userChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Category Performance & Geographic Data -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        <!-- Top Categories -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="800">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Top Categories</h3>
            <div class="space-y-4">
                <?php if(isset($categoryPerformance) && count($categoryPerformance) > 0): ?>
                    <?php $__currentLoopData = $categoryPerformance->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center mr-3">
                                <span class="text-white text-sm font-bold"><?php echo e(substr($category->name, 0, 2)); ?></span>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900"><?php echo e($category->name); ?></p>
                                <p class="text-sm text-gray-600"><?php echo e($category->events_count); ?> events</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900">Rp <?php echo e(number_format($category->total_revenue, 0, ',', '.')); ?></p>
                            <p class="text-sm text-gray-600"><?php echo e($category->tickets_sold); ?> tickets</p>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <div class="text-center py-8">
                        <i data-lucide="bar-chart-3" class="w-12 h-12 mx-auto mb-4 text-gray-400"></i>
                        <p class="text-gray-600">No category data available</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Geographic Distribution -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="900">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Top Cities</h3>
            <div class="space-y-4">
                <?php if(isset($geographicData) && count($geographicData) > 0): ?>
                    <?php $__currentLoopData = $geographicData->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                <i data-lucide="map-pin" class="w-4 h-4 text-blue-600"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900"><?php echo e($city->city); ?></p>
                                <p class="text-sm text-gray-600"><?php echo e($city->events_count); ?> events</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900"><?php echo e($city->tickets_sold); ?></p>
                            <p class="text-sm text-gray-600">tickets</p>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <div class="text-center py-8">
                        <i data-lucide="map" class="w-12 h-12 mx-auto mb-4 text-gray-400"></i>
                        <p class="text-gray-600">No geographic data available</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- System Health -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="1000">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">System Health</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-3 pulse"></div>
                        <span class="text-gray-700">Database</span>
                    </div>
                    <span class="text-green-600 font-medium">Healthy</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-3 pulse"></div>
                        <span class="text-gray-700">Storage</span>
                    </div>
                    <span class="text-green-600 font-medium"><?php echo e(isset($systemHealth['storage']) ? $systemHealth['storage'] : '85%'); ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3 pulse"></div>
                        <span class="text-gray-700">Cache</span>
                    </div>
                    <span class="text-yellow-600 font-medium"><?php echo e(isset($systemHealth['cache']) ? $systemHealth['cache'] : 'Optimizing'); ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-3 pulse"></div>
                        <span class="text-gray-700">API</span>
                    </div>
                    <span class="text-green-600 font-medium">Online</span>
                </div>
            </div>

            <div class="mt-6 pt-4 border-t border-gray-200">
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-600">Last Updated</span>
                    <span class="text-gray-900 font-medium"><?php echo e(now()->format('H:i:s')); ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Tickets & Users -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Tickets -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="500">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-lg font-semibold">Tiket Terbaru</h2>
                    <a href="<?php echo e(route('admin.tickets.index')); ?>" class="text-primary hover:text-primary/80 transition-colors">
                        Lihat Semua
                    </a>
                </div>
                <div class="space-y-4">
                    <?php $__empty_1 = true; $__currentLoopData = $recentActivities->where('type', 'event_published')->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-primary/10 rounded-lg flex items-center justify-center">
                            <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        <div class="flex-1 min-w-0">
                            <h3 class="font-medium truncate"><?php echo e($activity['event'] ?? 'Event'); ?></h3>
                            <p class="text-sm text-gray-600"><?php echo e($activity['description']); ?></p>
                            <p class="text-xs text-gray-500"><?php echo e($activity['created_at']->diffForHumans()); ?></p>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Published
                            </span>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                        <p class="text-gray-600">Belum ada tiket terbaru</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Users -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="600">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-lg font-semibold">Pengguna Terbaru</h2>
                    <a href="<?php echo e(route('admin.users.index')); ?>" class="text-primary hover:text-primary/80 transition-colors">
                        Lihat Semua
                    </a>
                </div>
                <div class="space-y-4">
                    <?php $__empty_1 = true; $__currentLoopData = $recentActivities->where('type', 'user_registration')->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                        </div>
                        <div class="flex-1 min-w-0">
                            <h3 class="font-medium truncate"><?php echo e($activity['user'] ?? 'User'); ?></h3>
                            <p class="text-sm text-gray-600 truncate"><?php echo e($activity['description']); ?></p>
                            <p class="text-xs text-gray-500"><?php echo e($activity['created_at']->diffForHumans()); ?></p>
                        </div>
                        <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-700">
                            Baru
                        </span>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                        <p class="text-gray-600">Belum ada pengguna baru</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Reports Modal -->
    <div id="reportsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-bold text-gray-900">📊 Generate Reports</h3>
                        <button onclick="hideReportsModal()" class="text-gray-400 hover:text-gray-600">
                            <i data-lucide="x" class="w-6 h-6"></i>
                        </button>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <button onclick="generateReport('revenue')" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                            <div class="flex items-center">
                                <div class="p-2 bg-green-100 rounded-lg mr-3">
                                    <i data-lucide="dollar-sign" class="w-5 h-5 text-green-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">Revenue Report</h4>
                                    <p class="text-sm text-gray-600">Financial analytics</p>
                                </div>
                            </div>
                        </button>

                        <button onclick="generateReport('users')" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                            <div class="flex items-center">
                                <div class="p-2 bg-blue-100 rounded-lg mr-3">
                                    <i data-lucide="users" class="w-5 h-5 text-blue-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">User Report</h4>
                                    <p class="text-sm text-gray-600">User analytics</p>
                                </div>
                            </div>
                        </button>

                        <button onclick="generateReport('events')" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                            <div class="flex items-center">
                                <div class="p-2 bg-purple-100 rounded-lg mr-3">
                                    <i data-lucide="calendar" class="w-5 h-5 text-purple-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">Events Report</h4>
                                    <p class="text-sm text-gray-600">Event performance</p>
                                </div>
                            </div>
                        </button>

                        <button onclick="generateReport('comprehensive')" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                            <div class="flex items-center">
                                <div class="p-2 bg-orange-100 rounded-lg mr-3">
                                    <i data-lucide="file-text" class="w-5 h-5 text-orange-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">Comprehensive</h4>
                                    <p class="text-sm text-gray-600">All metrics</p>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize counter animations
    initCounterAnimations();

    // Initialize charts
    initCharts();

    // Setup date range filter
    setupDateRangeFilter();

    // Auto refresh every 5 minutes
    setInterval(refreshDashboard, 300000);
});

// Counter animations
function initCounterAnimations() {
    const counters = document.querySelectorAll('.animate-counter');

    const animateCounter = (counter) => {
        const target = parseInt(counter.dataset.target);
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current).toLocaleString();
        }, 16);
    };

    // Use Intersection Observer
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    });

    counters.forEach(counter => observer.observe(counter));
}

// Initialize charts
function initCharts() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart');
    if (revenueCtx) {
        new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode($chartData['revenue']['labels'] ?? [], 15, 512) ?>,
                datasets: [{
                    label: 'Revenue',
                    data: <?php echo json_encode($chartData['revenue']['data'] ?? [], 15, 512) ?>,
                    borderColor: 'rgb(147, 51, 234)',
                    backgroundColor: 'rgba(147, 51, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rp ' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }

    // User Growth Chart
    const userCtx = document.getElementById('userChart');
    if (userCtx) {
        new Chart(userCtx, {
            type: 'bar',
            data: {
                labels: <?php echo json_encode($chartData['users']['labels'] ?? [], 15, 512) ?>,
                datasets: [{
                    label: 'New Users',
                    data: <?php echo json_encode($chartData['users']['new_users'] ?? [], 15, 512) ?>,
                    backgroundColor: 'rgba(59, 130, 246, 0.8)',
                }, {
                    label: 'New Organizers',
                    data: <?php echo json_encode($chartData['users']['new_organizers'] ?? [], 15, 512) ?>,
                    backgroundColor: 'rgba(34, 197, 94, 0.8)',
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

// Date range filter
function setupDateRangeFilter() {
    const dateRangeSelect = document.getElementById('dateRange');
    if (dateRangeSelect) {
        dateRangeSelect.addEventListener('change', function() {
            const range = this.value;
            window.location.href = `<?php echo e(route('admin.dashboard')); ?>?range=${range}`;
        });
    }
}

// Refresh dashboard
function refreshDashboard() {
    const refreshBtn = document.querySelector('button[onclick="refreshDashboard()"]');
    if (refreshBtn) {
        const icon = refreshBtn.querySelector('i');
        icon.classList.add('animate-spin');

        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
}

// Export report
function exportReport() {
    const dateRange = document.getElementById('dateRange').value;
    window.open(`<?php echo e(route('admin.dashboard')); ?>/export?range=${dateRange}`, '_blank');
}

// Reports modal
function showReportsModal() {
    document.getElementById('reportsModal').classList.remove('hidden');
}

function hideReportsModal() {
    document.getElementById('reportsModal').classList.add('hidden');
}

function generateReport(type) {
    const dateRange = document.getElementById('dateRange').value;
    window.open(`<?php echo e(route('admin.dashboard')); ?>/report/${type}?range=${dateRange}`, '_blank');
    hideReportsModal();
}

// Chart period change
function changeChartPeriod(chart, period) {
    // Update button states
    const buttons = document.querySelectorAll('.chart-period-btn');
    buttons.forEach(btn => {
        btn.classList.remove('bg-primary', 'text-white');
        btn.classList.add('text-gray-600', 'hover:bg-gray-100');
    });

    event.target.classList.add('bg-primary', 'text-white');
    event.target.classList.remove('text-gray-600', 'hover:bg-gray-100');

    // Fetch new data (implement AJAX call here)
    console.log(`Changing ${chart} chart to ${period} period`);
}

// Real-time updates (if WebSocket is available)
if (window.Echo) {
    window.Echo.channel('admin-dashboard')
        .listen('DashboardUpdated', (e) => {
            // Update specific metrics without full page reload
            updateMetrics(e.data);
        });
}

function updateMetrics(data) {
    // Update counter values
    Object.keys(data).forEach(key => {
        const element = document.querySelector(`[data-target="${data[key]}"]`);
        if (element) {
            element.textContent = data[key].toLocaleString();
        }
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tikpro.my.id\resources\views/pages/admin/dashboard.blade.php ENDPATH**/ ?>