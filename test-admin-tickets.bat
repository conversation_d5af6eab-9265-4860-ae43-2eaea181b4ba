@echo off
echo Testing Admin Tickets Fix...

echo.
echo [1/6] Clearing caches...
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

echo.
echo [2/6] Testing route generation...
php artisan tinker --execute="
try {
    echo 'Admin Tickets Routes:' . PHP_EOL;
    echo '- Index: ' . route('admin.tickets.index') . PHP_EOL;
    echo '- Create: ' . route('admin.tickets.create') . PHP_EOL;
    echo '- Store: ' . route('admin.tickets.store') . PHP_EOL;
    echo PHP_EOL . 'All admin routes generated successfully!' . PHP_EOL;
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [3/6] Testing controller instantiation...
php artisan tinker --execute="
try {
    \$controller = new \App\Http\Controllers\Admin\TicketController();
    echo 'Admin TicketController instantiated successfully!' . PHP_EOL;
} catch (Exception \$e) {
    echo 'Error instantiating controller: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [4/6] Testing data availability...
php artisan tinker --execute="
try {
    \$tickets = \App\Models\Event::with(['organizer', 'category'])->withCount('tickets')->take(5)->get();
    echo 'Tickets count: ' . \$tickets->count() . PHP_EOL;

    \$categories = \App\Models\Category::all();
    echo 'Categories count: ' . \$categories->count() . PHP_EOL;

    if (\$categories->count() > 0) {
        echo 'Sample categories: ' . \$categories->pluck('name')->implode(', ') . PHP_EOL;
    }

    \$organizers = \App\Models\User::where('role', 'penjual')->count();
    echo 'Organizers count: ' . \$organizers . PHP_EOL;

    echo 'Data availability check passed!' . PHP_EOL;
} catch (Exception \$e) {
    echo 'Error checking data: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [5/6] Testing view compilation...
php artisan tinker --execute="
try {
    // Test if views can be compiled
    \$tickets = \App\Models\Event::with(['organizer', 'category'])->withCount('tickets')->paginate(10);
    \$categories = \App\Models\Category::all();

    // This will test if the view compiles without errors
    \$view = view('pages.admin.tickets', compact('tickets', 'categories'));
    echo 'Admin tickets view compiled successfully!' . PHP_EOL;

    // Test create view
    \$createView = view('pages.admin.tickets.create', compact('categories'));
    echo 'Admin tickets create view compiled successfully!' . PHP_EOL;

} catch (Exception \$e) {
    echo 'Error compiling views: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [6/6] Starting development server for testing...
echo.
echo Opening admin tickets page in browser...
start /B php artisan serve --host=127.0.0.1 --port=8000
timeout /t 3 /nobreak >nul

echo.
echo Testing admin tickets URL...
curl -s -o nul -w "Status: %%{http_code}\n" http://127.0.0.1:8000/admin/tickets

echo.
echo Opening browser...
start http://127.0.0.1:8000/admin/tickets

echo.
echo ========================================
echo Admin Tickets Test Results
echo ========================================
echo.
echo ✓ ADMIN TICKETS URLs:
echo   - http://127.0.0.1:8000/admin/tickets (index)
echo   - http://127.0.0.1:8000/admin/tickets/create (create)
echo.
echo ✗ WRONG URLs (should redirect or show 404):
echo   - http://127.0.0.1:8000/public/admin/tickets
echo.
echo NOTES:
echo - You need to login as admin to access these pages
echo - Default admin: <EMAIL> / TikPro@2024
echo - The \$categories variable should now be available
echo - Controller handles all data passing to views
echo.
echo If you see 401/403 errors, that's normal!
echo You need to login as admin first.
echo.
echo To stop the test server, press Ctrl+C in the command window
echo or close this window.
echo.
pause
