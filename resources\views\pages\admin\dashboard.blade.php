@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Dashboard Header -->
    <div class="mb-8" data-aos="fade-up">
        <h1 class="text-2xl font-bold mb-2">Dashboard Admin</h1>
        <p class="text-gray-600">Kelola event, pengguna, dan statistik penjualan</p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Tickets -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-primary/10 rounded-lg">
                    <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Total Event</p>
                    <p class="text-2xl font-bold">{{ number_format($platformStats['total_tickets'], 0, ',', '.') }}</p>
                </div>
            </div>
            <div class="flex items-center text-sm">
                <span class="text-green-600 font-medium">{{ number_format($platformStats['published_tickets'], 0, ',', '.') }} Published</span>
                <span class="text-gray-400 mx-2">•</span>
                <span class="text-orange-600">{{ number_format($platformStats['new_tickets'], 0, ',', '.') }} Baru</span>
            </div>
        </div>

        <!-- Total Users -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="200">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-blue-50 rounded-lg">
                    <svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Total Pengguna</p>
                    <p class="text-2xl font-bold">{{ number_format($platformStats['total_users'], 0, ',', '.') }}</p>
                </div>
            </div>
            <div class="flex items-center text-sm">
                <span class="text-green-600 font-medium">
                    +{{ number_format($platformStats['new_users'], 0, ',', '.') }}
                </span>
                <span class="text-gray-400 mx-2">•</span>
                <span class="text-gray-600">{{ $dateRange }} hari terakhir</span>
            </div>
        </div>

        <!-- Total Sales -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="300">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-green-50 rounded-lg">
                    <svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Total Revenue</p>
                    <p class="text-2xl font-bold">Rp {{ number_format($platformStats['total_revenue'], 0, ',', '.') }}</p>
                </div>
            </div>
            <div class="flex items-center text-sm">
                <span class="text-green-600 font-medium">
                    +Rp {{ number_format($platformStats['period_revenue'], 0, ',', '.') }}
                </span>
                <span class="text-gray-400 mx-2">•</span>
                <span class="text-gray-600">{{ $dateRange }} hari terakhir</span>
            </div>
        </div>

        <!-- Active Tickets -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="400">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-purple-50 rounded-lg">
                    <svg class="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Tiket Terjual</p>
                    <p class="text-2xl font-bold">{{ number_format($platformStats['total_tickets_sold'], 0, ',', '.') }}</p>
                </div>
            </div>
            <div class="flex items-center text-sm">
                <span class="text-purple-600 font-medium">
                    +{{ number_format($platformStats['period_tickets_sold'], 0, ',', '.') }}
                </span>
                <span class="text-gray-400 mx-2">•</span>
                <span class="text-gray-600">{{ $dateRange }} hari terakhir</span>
            </div>
        </div>
    </div>

    <!-- Management Features -->
    <div class="mb-8" data-aos="fade-up" data-aos-delay="500">
        <h2 class="text-xl font-bold mb-6">Fitur Manajemen</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Manage Tickets -->
            <a href="{{ route('admin.tickets.index') }}" class="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow duration-300">
                <div class="flex items-center justify-between">
                    <div class="p-3 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600">Kelola</p>
                        <p class="text-lg font-semibold text-gray-900">Tickets</p>
                    </div>
                </div>
            </a>

            <!-- Manage Users -->
            <a href="{{ route('admin.users.index') }}" class="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow duration-300">
                <div class="flex items-center justify-between">
                    <div class="p-3 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                        </svg>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600">Kelola</p>
                        <p class="text-lg font-semibold text-gray-900">Users</p>
                    </div>
                </div>
            </a>

            <!-- Manage Payments -->
            <a href="{{ route('admin.payments.index') }}" class="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow duration-300">
                <div class="flex items-center justify-between">
                    <div class="p-3 bg-purple-100 rounded-lg">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                        </svg>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600">Kelola</p>
                        <p class="text-lg font-semibold text-gray-900">Payments</p>
                    </div>
                </div>
            </a>

            <!-- Manage Organizers -->
            <a href="{{ route('admin.organizers.index') }}" class="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow duration-300">
                <div class="flex items-center justify-between">
                    <div class="p-3 bg-orange-100 rounded-lg">
                        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600">Kelola</p>
                        <p class="text-lg font-semibold text-gray-900">Organizers</p>
                    </div>
                </div>
            </a>
        </div>

        <!-- Additional Management Features -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
            <!-- Manage Notifications -->
            <a href="{{ route('admin.notifications.index') }}" class="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow duration-300">
                <div class="flex items-center justify-between">
                    <div class="p-3 bg-indigo-100 rounded-lg">
                        <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h8V9H4v2z"/>
                        </svg>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600">Kelola</p>
                        <p class="text-lg font-semibold text-gray-900">Notifications</p>
                    </div>
                </div>
            </a>

            <!-- View Reports -->
            <a href="#" class="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow duration-300">
                <div class="flex items-center justify-between">
                    <div class="p-3 bg-pink-100 rounded-lg">
                        <svg class="w-6 h-6 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600">Lihat</p>
                        <p class="text-lg font-semibold text-gray-900">Reports</p>
                    </div>
                </div>
            </a>

            <!-- System Settings -->
            <a href="#" class="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow duration-300">
                <div class="flex items-center justify-between">
                    <div class="p-3 bg-teal-100 rounded-lg">
                        <svg class="w-6 h-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600">System</p>
                        <p class="text-lg font-semibold text-gray-900">Settings</p>
                    </div>
                </div>
            </a>
        </div>
    </div>

    <!-- Recent Tickets & Users -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Tickets -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="500">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-lg font-semibold">Tiket Terbaru</h2>
                    <a href="{{ route('admin.tickets.index') }}" class="text-primary hover:text-primary/80 transition-colors">
                        Lihat Semua
                    </a>
                </div>
                <div class="space-y-4">
                    @forelse($recentActivities->where('type', 'event_published')->take(5) as $activity)
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-primary/10 rounded-lg flex items-center justify-center">
                            <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        <div class="flex-1 min-w-0">
                            <h3 class="font-medium truncate">{{ $activity['event'] ?? 'Event' }}</h3>
                            <p class="text-sm text-gray-600">{{ $activity['description'] }}</p>
                            <p class="text-xs text-gray-500">{{ $activity['created_at']->diffForHumans() }}</p>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Published
                            </span>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                        <p class="text-gray-600">Belum ada tiket terbaru</p>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Recent Users -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="600">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-lg font-semibold">Pengguna Terbaru</h2>
                    <a href="{{ route('admin.users.index') }}" class="text-primary hover:text-primary/80 transition-colors">
                        Lihat Semua
                    </a>
                </div>
                <div class="space-y-4">
                    @forelse($recentActivities->where('type', 'user_registration')->take(5) as $activity)
                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                        </div>
                        <div class="flex-1 min-w-0">
                            <h3 class="font-medium truncate">{{ $activity['user'] ?? 'User' }}</h3>
                            <p class="text-sm text-gray-600 truncate">{{ $activity['description'] }}</p>
                            <p class="text-xs text-gray-500">{{ $activity['created_at']->diffForHumans() }}</p>
                        </div>
                        <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-700">
                            Baru
                        </span>
                    </div>
                    @empty
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                        <p class="text-gray-600">Belum ada pengguna baru</p>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
</div>
@endsection