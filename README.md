# 🎫 TikPro - Event Ticketing Management System

<p align="center">
  <img src="https://img.shields.io/badge/Laravel-10.x-red.svg" alt="Laravel Version">
  <img src="https://img.shields.io/badge/PHP-8.1+-blue.svg" alt="PHP Version">
  <img src="https://img.shields.io/badge/MySQL-8.0+-orange.svg" alt="MySQL Version">
  <img src="https://img.shields.io/badge/License-MIT-green.svg" alt="License">
</p>

## 📋 About TikPro

TikPro adalah sistem manajemen tiket event yang komprehensif yang dibangun dengan Laravel 10. Aplikasi ini menyediakan platform lengkap untuk mengelola event, penjualan tiket, dan manajemen pengguna dengan antarmuka yang modern dan responsif.

### ✨ Fitur Utama

- **🎪 Event Management** - Kelola event dengan mudah (CRUD, kategori, venue)
- **🎫 Ticket Sales** - Sistem penjualan tiket digital dengan QR code
- **👥 User Management** - Multi-role system (Admin, Staff, Penjual, Pembeli)
- **💳 Payment Integration** - Integrasi pembayaran digital
- **📱 PWA Support** - Progressive Web App untuk mobile experience
- **🌙 Dark/Light Theme** - Toggle tema gelap dan terang
- **🔔 Real-time Notifications** - Notifikasi live dengan sound alerts
- **📊 Analytics Dashboard** - Dashboard analitik untuk admin dan organizer
- **🎨 Modern UI** - Desain modern dengan Tailwind CSS dan animasi

## 🚀 Quick Start

### Prerequisites

- PHP 8.1 atau lebih tinggi
- Composer
- MySQL 8.0+
- Node.js & NPM (untuk asset compilation)
- Laragon/XAMPP/WAMP (untuk development lokal)

### Installation

1. **Clone Repository**
   ```bash
   git clone https://github.com/your-username/tikpro.git
   cd tikpro
   ```

2. **Install Dependencies**
   ```bash
   composer install
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Database Configuration**
   Edit file `.env` dan sesuaikan konfigurasi database:
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=db_tikpro
   DB_USERNAME=root
   DB_PASSWORD=
   ```

5. **Database Migration & Seeding**
   ```bash
   php artisan migrate:fresh --seed
   ```

6. **Compile Assets**
   ```bash
   npm run dev
   # atau untuk production
   npm run build
   ```

7. **Start Development Server**
   ```bash
   php artisan serve
   ```

   Aplikasi akan berjalan di `http://localhost:8000`

## Laravel Sponsors

We would like to extend our thanks to the following sponsors for funding Laravel development. If you are interested in becoming a sponsor, please visit the [Laravel Partners program](https://partners.laravel.com).

### Premium Partners

- **[Vehikl](https://vehikl.com/)**
- **[Tighten Co.](https://tighten.co)**
- **[WebReinvent](https://webreinvent.com/)**
- **[Kirschbaum Development Group](https://kirschbaumdevelopment.com)**
- **[64 Robots](https://64robots.com)**
- **[Curotec](https://www.curotec.com/services/technologies/laravel/)**
- **[Cyber-Duck](https://cyber-duck.co.uk)**
- **[DevSquad](https://devsquad.com/hire-laravel-developers)**
- **[Jump24](https://jump24.co.uk)**
- **[Redberry](https://redberry.international/laravel/)**
- **[Active Logic](https://activelogic.com)**
- **[byte5](https://byte5.de)**
- **[OP.GG](https://op.gg)**

## Contributing

Thank you for considering contributing to the Laravel framework! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

If you discover a security vulnerability within Laravel, please send an e-mail to Taylor Otwell via [<EMAIL>](mailto:<EMAIL>). All security vulnerabilities will be promptly addressed.

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
